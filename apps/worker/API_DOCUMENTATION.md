# Market Data API Documentation

This document describes the REST API endpoints available in the Worker service for retrieving historical market data from the MongoDB time series database.

## Base URL

```
http://localhost:3000/api/market-data
```

## Authentication

Currently, all endpoints are public and do not require authentication.

## Response Format

All API responses follow a consistent format:

```json
{
  "success": boolean,
  "data": any,
  "message": string,
  "timestamp": string,
  "count": number (optional)
}
```

Error responses include additional fields:
```json
{
  "success": false,
  "error": string,
  "message": string,
  "timestamp": string,
  "details": array (optional)
}
```

## Endpoints

### 1. Health Check

**GET** `/health`

Returns the health status of the API and its dependencies.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "service": "market-data-api",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600,
    "database": {
      "mongodb": "connected",
      "redis": "connected"
    },
    "version": "1.0.0"
  }
}
```

### 2. Get Currency Pairs

**GET** `/pairs`

Returns all available currency pairs in the database.

**Response:**
```json
{
  "success": true,
  "data": {
    "pairs": ["btcusdt", "ETH/USDT", "ADA/USDT"],
    "count": 3,
    "lastUpdated": "2024-01-01T00:00:00.000Z"
  }
}
```

### 3. Get Latest Data

**GET** `/latest/:currencyPair`

Returns the most recent market data for a specific currency pair.

**Parameters:**
- `currencyPair` (path): Currency pair (e.g., "btcusdt")

**Example:**
```
GET /latest/BTC%2FUSDT
```

**Response:**
```json
{
  "success": true,
  "data": {
    "currencyPair": "btcusdt",
    "data": {
      "currencyPair": "btcusdt",
      "lastPrice": "45000.00",
      "baseVolume": "1234.56",
      "timestamp": "2024-01-01T00:00:00.000Z",
      // ... other market data fields
    },
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### 4. Get Historical Data

**GET** `/historical`

Returns historical market data for a specific currency pair within a date range.

**Query Parameters:**
- `currencyPair` (required): Currency pair (e.g., "btcusdt")
- `start` (required): Start date in ISO format (e.g., "2024-01-01T00:00:00.000Z")
- `end` (required): End date in ISO format (e.g., "2024-01-02T00:00:00.000Z")
- `limit` (optional): Maximum number of records (max 10,000)

**Example:**
```
GET /historical?currencyPair=btcusdt&start=2024-01-01T00:00:00.000Z&end=2024-01-02T00:00:00.000Z&limit=100
```

**Response:**
```json
{
  "success": true,
  "data": {
    "currencyPair": "btcusdt",
    "data": [
      {
        "currencyPair": "btcusdt",
        "lastPrice": "45000.00",
        "baseVolume": "1234.56",
        "timestamp": "2024-01-01T00:00:00.000Z",
        // ... other fields
      }
    ],
    "count": 100,
    "dateRange": {
      "start": "2024-01-01T00:00:00.000Z",
      "end": "2024-01-02T00:00:00.000Z"
    }
  },
  "count": 100
}
```

### 5. Get Aggregated Data

**GET** `/aggregated`

Returns aggregated market data with OHLC-like statistics for a specific currency pair and time interval.

**Query Parameters:**
- `currencyPair` (required): Currency pair (e.g., "btcusdt")
- `start` (required): Start date in ISO format
- `end` (required): End date in ISO format
- `interval` (required): Aggregation interval ("minute", "hour", "day", "week", "month")

**Example:**
```
GET /aggregated?currencyPair=btcusdt&start=2024-01-01T00:00:00.000Z&end=2024-01-02T00:00:00.000Z&interval=hour
```

**Response:**
```json
{
  "success": true,
  "data": {
    "currencyPair": "btcusdt",
    "interval": "hour",
    "data": [
      {
        "period": "2024-01-01 00:00:00",
        "averagePrice": 45000.00,
        "minPrice": 44500.00,
        "maxPrice": 45500.00,
        "totalVolume": 12345.67,
        "count": 60,
        "firstPrice": 44800.00,
        "lastPrice": 45200.00
      }
    ],
    "count": 24,
    "dateRange": {
      "start": "2024-01-01T00:00:00.000Z",
      "end": "2024-01-02T00:00:00.000Z"
    }
  },
  "count": 24
}
```

### 6. Get Data by Range

**GET** `/range`

Returns market data within a date range with flexible filtering options.

**Query Parameters:**
- `start` (required): Start date in ISO format
- `end` (required): End date in ISO format
- `currencyPair` (optional): Currency pair filter
- `limit` (optional): Maximum number of records
- `sortOrder` (optional): Sort order ("asc" or "desc", default: "asc")

**Example:**
```
GET /range?start=2024-01-01T00:00:00.000Z&end=2024-01-02T00:00:00.000Z&currencyPair=btcusdt&limit=100&sortOrder=desc
```

### 7. Get Market Statistics

**GET** `/statistics/:currencyPair`

Returns comprehensive market statistics for a currency pair over a specified period.

**Parameters:**
- `currencyPair` (path): Currency pair (e.g., "btcusdt")

**Query Parameters:**
- `period` (optional): Time period ("24h", "7d", "30d", default: "24h")

**Example:**
```
GET /statistics/BTC%2FUSDT?period=24h
```

**Response:**
```json
{
  "success": true,
  "data": {
    "currencyPair": "btcusdt",
    "period": "24h",
    "totalRecords": 1440,
    "priceStats": {
      "current": 45000.00,
      "min": 44000.00,
      "max": 46000.00,
      "average": 45000.00,
      "change": 500.00,
      "changePercent": 1.12
    },
    "volumeStats": {
      "total": 123456.78,
      "average": 85.73,
      "min": 10.00,
      "max": 500.00
    },
    "dateRange": {
      "start": "2024-01-01T00:00:00.000Z",
      "end": "2024-01-02T00:00:00.000Z"
    }
  }
}
```

## Error Handling

The API returns appropriate HTTP status codes:

- `200` - Success
- `400` - Bad Request (validation errors)
- `404` - Not Found
- `422` - Unprocessable Entity (validation failed)
- `500` - Internal Server Error
- `503` - Service Unavailable

## Validation Rules

### Currency Pair Format
- Must be a string in format: `XXX/YYY` or `XXX_YYY`
- Each part must be 2-10 uppercase letters
- Examples: `btcusdt`, `ETH_USDT`

### Date Format
- Must be valid ISO 8601 date strings
- Examples: `2024-01-01T00:00:00.000Z`, `2024-01-01`

### Date Range
- Start date must be before end date
- Maximum range is 1 year
- Dates cannot be in the future

### Limits
- Historical data limit: 1-10,000 records
- Default pagination: 100 records per page

## Rate Limiting

Currently, no rate limiting is implemented. Consider implementing rate limiting for production use.

## Examples

### Get latest Bitcoin price
```bash
curl "http://localhost:3000/api/market-data/latest/BTC%2FUSDT"
```

### Get hourly aggregated data for the last 24 hours
```bash
curl "http://localhost:3000/api/market-data/aggregated?currencyPair=btcusdt&start=2024-01-01T00:00:00.000Z&end=2024-01-02T00:00:00.000Z&interval=hour"
```

### Get market statistics for the last 7 days
```bash
curl "http://localhost:3000/api/market-data/statistics/BTC%2FUSDT?period=7d"
```
