<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Market Data API Documentation</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1 id="market-data-api-documentation">Market Data API Documentation</h1>
<p>This document describes the REST API endpoints available in the Worker service for retrieving historical market data from the MongoDB time series database.</p>
<h2 id="base-url">Base URL</h2>
<pre><code>http://localhost:3000/api/market-data
</code></pre>
<h2 id="authentication">Authentication</h2>
<p>Currently, all endpoints are public and do not require authentication.</p>
<h2 id="response-format">Response Format</h2>
<p>All API responses follow a consistent format:</p>
<pre><code class="language-json"><span class="hljs-punctuation">{</span>
  <span class="hljs-attr">&quot;success&quot;</span><span class="hljs-punctuation">:</span> boolean<span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;data&quot;</span><span class="hljs-punctuation">:</span> any<span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;message&quot;</span><span class="hljs-punctuation">:</span> string<span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;timestamp&quot;</span><span class="hljs-punctuation">:</span> string<span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;count&quot;</span><span class="hljs-punctuation">:</span> number (optional)
<span class="hljs-punctuation">}</span>
</code></pre>
<p>Error responses include additional fields:</p>
<pre><code class="language-json"><span class="hljs-punctuation">{</span>
  <span class="hljs-attr">&quot;success&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-keyword">false</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;error&quot;</span><span class="hljs-punctuation">:</span> string<span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;message&quot;</span><span class="hljs-punctuation">:</span> string<span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;timestamp&quot;</span><span class="hljs-punctuation">:</span> string<span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;details&quot;</span><span class="hljs-punctuation">:</span> array (optional)
<span class="hljs-punctuation">}</span>
</code></pre>
<h2 id="endpoints">Endpoints</h2>
<h3 id="1-health-check">1. Health Check</h3>
<p><strong>GET</strong> <code>/health</code></p>
<p>Returns the health status of the API and its dependencies.</p>
<p><strong>Response:</strong></p>
<pre><code class="language-json"><span class="hljs-punctuation">{</span>
  <span class="hljs-attr">&quot;success&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-keyword">true</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;data&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
    <span class="hljs-attr">&quot;status&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;healthy&quot;</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;service&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;market-data-api&quot;</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;timestamp&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-01T00:00:00.000Z&quot;</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;uptime&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">3600</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;database&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
      <span class="hljs-attr">&quot;mongodb&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;connected&quot;</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;redis&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;connected&quot;</span>
    <span class="hljs-punctuation">}</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;version&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;1.0.0&quot;</span>
  <span class="hljs-punctuation">}</span>
<span class="hljs-punctuation">}</span>
</code></pre>
<h3 id="2-get-currency-pairs">2. Get Currency Pairs</h3>
<p><strong>GET</strong> <code>/pairs</code></p>
<p>Returns all available currency pairs in the database.</p>
<p><strong>Response:</strong></p>
<pre><code class="language-json"><span class="hljs-punctuation">{</span>
  <span class="hljs-attr">&quot;success&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-keyword">true</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;data&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
    <span class="hljs-attr">&quot;pairs&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">[</span><span class="hljs-string">&quot;btcusdt&quot;</span><span class="hljs-punctuation">,</span> <span class="hljs-string">&quot;ETH/USDT&quot;</span><span class="hljs-punctuation">,</span> <span class="hljs-string">&quot;ADA/USDT&quot;</span><span class="hljs-punctuation">]</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;count&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">3</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;lastUpdated&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-01T00:00:00.000Z&quot;</span>
  <span class="hljs-punctuation">}</span>
<span class="hljs-punctuation">}</span>
</code></pre>
<h3 id="3-get-latest-data">3. Get Latest Data</h3>
<p><strong>GET</strong> <code>/latest/:currencyPair</code></p>
<p>Returns the most recent market data for a specific currency pair.</p>
<p><strong>Parameters:</strong></p>
<ul>
<li><code>currencyPair</code> (path): Currency pair (e.g., &quot;btcusdt&quot;)</li>
</ul>
<p><strong>Example:</strong></p>
<pre><code>GET /latest/BTC%2FUSDT
</code></pre>
<p><strong>Response:</strong></p>
<pre><code class="language-json"><span class="hljs-punctuation">{</span>
  <span class="hljs-attr">&quot;success&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-keyword">true</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;data&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
    <span class="hljs-attr">&quot;currencyPair&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;btcusdt&quot;</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;data&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
      <span class="hljs-attr">&quot;currencyPair&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;btcusdt&quot;</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;lastPrice&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;45000.00&quot;</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;baseVolume&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;1234.56&quot;</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;timestamp&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-01T00:00:00.000Z&quot;</span><span class="hljs-punctuation">,</span>
      <span class="hljs-comment">// ... other market data fields</span>
    <span class="hljs-punctuation">}</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;timestamp&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-01T00:00:00.000Z&quot;</span>
  <span class="hljs-punctuation">}</span>
<span class="hljs-punctuation">}</span>
</code></pre>
<h3 id="4-get-historical-data">4. Get Historical Data</h3>
<p><strong>GET</strong> <code>/historical</code></p>
<p>Returns historical market data for a specific currency pair within a date range.</p>
<p><strong>Query Parameters:</strong></p>
<ul>
<li><code>currencyPair</code> (required): Currency pair (e.g., &quot;btcusdt&quot;)</li>
<li><code>start</code> (required): Start date in ISO format (e.g., &quot;2024-01-01T00:00:00.000Z&quot;)</li>
<li><code>end</code> (required): End date in ISO format (e.g., &quot;2024-01-02T00:00:00.000Z&quot;)</li>
<li><code>limit</code> (optional): Maximum number of records (max 10,000)</li>
</ul>
<p><strong>Example:</strong></p>
<pre><code>GET /historical?currencyPair=btcusdt&amp;start=2024-01-01T00:00:00.000Z&amp;end=2024-01-02T00:00:00.000Z&amp;limit=100
</code></pre>
<p><strong>Response:</strong></p>
<pre><code class="language-json"><span class="hljs-punctuation">{</span>
  <span class="hljs-attr">&quot;success&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-keyword">true</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;data&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
    <span class="hljs-attr">&quot;currencyPair&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;btcusdt&quot;</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;data&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">[</span>
      <span class="hljs-punctuation">{</span>
        <span class="hljs-attr">&quot;currencyPair&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;btcusdt&quot;</span><span class="hljs-punctuation">,</span>
        <span class="hljs-attr">&quot;lastPrice&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;45000.00&quot;</span><span class="hljs-punctuation">,</span>
        <span class="hljs-attr">&quot;baseVolume&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;1234.56&quot;</span><span class="hljs-punctuation">,</span>
        <span class="hljs-attr">&quot;timestamp&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-01T00:00:00.000Z&quot;</span><span class="hljs-punctuation">,</span>
        <span class="hljs-comment">// ... other fields</span>
      <span class="hljs-punctuation">}</span>
    <span class="hljs-punctuation">]</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;count&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">100</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;dateRange&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
      <span class="hljs-attr">&quot;start&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-01T00:00:00.000Z&quot;</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;end&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-02T00:00:00.000Z&quot;</span>
    <span class="hljs-punctuation">}</span>
  <span class="hljs-punctuation">}</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;count&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">100</span>
<span class="hljs-punctuation">}</span>
</code></pre>
<h3 id="5-get-aggregated-data">5. Get Aggregated Data</h3>
<p><strong>GET</strong> <code>/aggregated</code></p>
<p>Returns aggregated market data with OHLC-like statistics for a specific currency pair and time interval.</p>
<p><strong>Query Parameters:</strong></p>
<ul>
<li><code>currencyPair</code> (required): Currency pair (e.g., &quot;btcusdt&quot;)</li>
<li><code>start</code> (required): Start date in ISO format</li>
<li><code>end</code> (required): End date in ISO format</li>
<li><code>interval</code> (required): Aggregation interval (&quot;minute&quot;, &quot;hour&quot;, &quot;day&quot;, &quot;week&quot;, &quot;month&quot;)</li>
</ul>
<p><strong>Example:</strong></p>
<pre><code>GET /aggregated?currencyPair=btcusdt&amp;start=2024-01-01T00:00:00.000Z&amp;end=2024-01-02T00:00:00.000Z&amp;interval=hour
</code></pre>
<p><strong>Response:</strong></p>
<pre><code class="language-json"><span class="hljs-punctuation">{</span>
  <span class="hljs-attr">&quot;success&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-keyword">true</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;data&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
    <span class="hljs-attr">&quot;currencyPair&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;btcusdt&quot;</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;interval&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;hour&quot;</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;data&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">[</span>
      <span class="hljs-punctuation">{</span>
        <span class="hljs-attr">&quot;period&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-01 00:00:00&quot;</span><span class="hljs-punctuation">,</span>
        <span class="hljs-attr">&quot;averagePrice&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">45000.00</span><span class="hljs-punctuation">,</span>
        <span class="hljs-attr">&quot;minPrice&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">44500.00</span><span class="hljs-punctuation">,</span>
        <span class="hljs-attr">&quot;maxPrice&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">45500.00</span><span class="hljs-punctuation">,</span>
        <span class="hljs-attr">&quot;totalVolume&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">12345.67</span><span class="hljs-punctuation">,</span>
        <span class="hljs-attr">&quot;count&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">60</span><span class="hljs-punctuation">,</span>
        <span class="hljs-attr">&quot;firstPrice&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">44800.00</span><span class="hljs-punctuation">,</span>
        <span class="hljs-attr">&quot;lastPrice&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">45200.00</span>
      <span class="hljs-punctuation">}</span>
    <span class="hljs-punctuation">]</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;count&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">24</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;dateRange&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
      <span class="hljs-attr">&quot;start&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-01T00:00:00.000Z&quot;</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;end&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-02T00:00:00.000Z&quot;</span>
    <span class="hljs-punctuation">}</span>
  <span class="hljs-punctuation">}</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;count&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">24</span>
<span class="hljs-punctuation">}</span>
</code></pre>
<h3 id="6-get-data-by-range">6. Get Data by Range</h3>
<p><strong>GET</strong> <code>/range</code></p>
<p>Returns market data within a date range with flexible filtering options.</p>
<p><strong>Query Parameters:</strong></p>
<ul>
<li><code>start</code> (required): Start date in ISO format</li>
<li><code>end</code> (required): End date in ISO format</li>
<li><code>currencyPair</code> (optional): Currency pair filter</li>
<li><code>limit</code> (optional): Maximum number of records</li>
<li><code>sortOrder</code> (optional): Sort order (&quot;asc&quot; or &quot;desc&quot;, default: &quot;asc&quot;)</li>
</ul>
<p><strong>Example:</strong></p>
<pre><code>GET /range?start=2024-01-01T00:00:00.000Z&amp;end=2024-01-02T00:00:00.000Z&amp;currencyPair=btcusdt&amp;limit=100&amp;sortOrder=desc
</code></pre>
<h3 id="7-get-market-statistics">7. Get Market Statistics</h3>
<p><strong>GET</strong> <code>/statistics/:currencyPair</code></p>
<p>Returns comprehensive market statistics for a currency pair over a specified period.</p>
<p><strong>Parameters:</strong></p>
<ul>
<li><code>currencyPair</code> (path): Currency pair (e.g., &quot;btcusdt&quot;)</li>
</ul>
<p><strong>Query Parameters:</strong></p>
<ul>
<li><code>period</code> (optional): Time period (&quot;24h&quot;, &quot;7d&quot;, &quot;30d&quot;, default: &quot;24h&quot;)</li>
</ul>
<p><strong>Example:</strong></p>
<pre><code>GET /statistics/BTC%2FUSDT?period=24h
</code></pre>
<p><strong>Response:</strong></p>
<pre><code class="language-json"><span class="hljs-punctuation">{</span>
  <span class="hljs-attr">&quot;success&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-keyword">true</span><span class="hljs-punctuation">,</span>
  <span class="hljs-attr">&quot;data&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
    <span class="hljs-attr">&quot;currencyPair&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;btcusdt&quot;</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;period&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;24h&quot;</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;totalRecords&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">1440</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;priceStats&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
      <span class="hljs-attr">&quot;current&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">45000.00</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;min&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">44000.00</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;max&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">46000.00</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;average&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">45000.00</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;change&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">500.00</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;changePercent&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">1.12</span>
    <span class="hljs-punctuation">}</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;volumeStats&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
      <span class="hljs-attr">&quot;total&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">123456.78</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;average&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">85.73</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;min&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">10.00</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;max&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-number">500.00</span>
    <span class="hljs-punctuation">}</span><span class="hljs-punctuation">,</span>
    <span class="hljs-attr">&quot;dateRange&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-punctuation">{</span>
      <span class="hljs-attr">&quot;start&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-01T00:00:00.000Z&quot;</span><span class="hljs-punctuation">,</span>
      <span class="hljs-attr">&quot;end&quot;</span><span class="hljs-punctuation">:</span> <span class="hljs-string">&quot;2024-01-02T00:00:00.000Z&quot;</span>
    <span class="hljs-punctuation">}</span>
  <span class="hljs-punctuation">}</span>
<span class="hljs-punctuation">}</span>
</code></pre>
<h2 id="error-handling">Error Handling</h2>
<p>The API returns appropriate HTTP status codes:</p>
<ul>
<li><code>200</code> - Success</li>
<li><code>400</code> - Bad Request (validation errors)</li>
<li><code>404</code> - Not Found</li>
<li><code>422</code> - Unprocessable Entity (validation failed)</li>
<li><code>500</code> - Internal Server Error</li>
<li><code>503</code> - Service Unavailable</li>
</ul>
<h2 id="validation-rules">Validation Rules</h2>
<h3 id="currency-pair-format">Currency Pair Format</h3>
<ul>
<li>Must be a string in format: <code>XXX/YYY</code> or <code>XXX_YYY</code></li>
<li>Each part must be 2-10 uppercase letters</li>
<li>Examples: <code>btcusdt</code>, <code>ETH_USDT</code></li>
</ul>
<h3 id="date-format">Date Format</h3>
<ul>
<li>Must be valid ISO 8601 date strings</li>
<li>Examples: <code>2024-01-01T00:00:00.000Z</code>, <code>2024-01-01</code></li>
</ul>
<h3 id="date-range">Date Range</h3>
<ul>
<li>Start date must be before end date</li>
<li>Maximum range is 1 year</li>
<li>Dates cannot be in the future</li>
</ul>
<h3 id="limits">Limits</h3>
<ul>
<li>Historical data limit: 1-10,000 records</li>
<li>Default pagination: 100 records per page</li>
</ul>
<h2 id="rate-limiting">Rate Limiting</h2>
<p>Currently, no rate limiting is implemented. Consider implementing rate limiting for production use.</p>
<h2 id="examples">Examples</h2>
<h3 id="get-latest-bitcoin-price">Get latest Bitcoin price</h3>
<pre><code class="language-bash">curl <span class="hljs-string">&quot;http://localhost:3000/api/market-data/latest/BTC%2FUSDT&quot;</span>
</code></pre>
<h3 id="get-hourly-aggregated-data-for-the-last-24-hours">Get hourly aggregated data for the last 24 hours</h3>
<pre><code class="language-bash">curl <span class="hljs-string">&quot;http://localhost:3000/api/market-data/aggregated?currencyPair=btcusdt&amp;start=2024-01-01T00:00:00.000Z&amp;end=2024-01-02T00:00:00.000Z&amp;interval=hour&quot;</span>
</code></pre>
<h3 id="get-market-statistics-for-the-last-7-days">Get market statistics for the last 7 days</h3>
<pre><code class="language-bash">curl <span class="hljs-string">&quot;http://localhost:3000/api/market-data/statistics/BTC%2FUSDT?period=7d&quot;</span>
</code></pre>

            
            
        </body>
        </html>